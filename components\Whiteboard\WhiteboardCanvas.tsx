'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';

export interface WhiteboardTool {
  type: 'pen' | 'rectangle' | 'circle' | 'triangle' | 'arrow' | 'line' | 'text' | 'eraser' | 'select';
  color: string;
  strokeWidth: number;
  fillColor?: string;
}

interface WhiteboardCanvasProps {
  tool: WhiteboardTool;
  onToolChange?: (tool: WhiteboardTool) => void;
}

interface WhiteboardCanvasRef {
  exportAsImage: () => void;
  exportAsSVG: () => void;
  clearCanvas: () => void;
  undo: () => void;
}

interface DrawingElement {
  id: string;
  type: string;
  startX: number;
  startY: number;
  endX?: number;
  endY?: number;
  color: string;
  strokeWidth: number;
  fillColor?: string;
  path?: { x: number; y: number }[];
  text?: string;
  width?: number;
  height?: number;
}

const WhiteboardCanvas = React.forwardRef<WhiteboardCanvasRef, WhiteboardCanvasProps>(
  ({ tool, onToolChange }, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [elements, setElements] = useState<DrawingElement[]>([]);
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>([]);
  const [selectedElement, setSelectedElement] = useState<DrawingElement | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;

    contextRef.current = context;

    // Set canvas size
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight - 160;

    // Set up canvas properties
    context.lineCap = 'round';
    context.lineJoin = 'round';

    // Handle window resize
    const handleResize = () => {
      const tempElements = [...elements];
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight - 160;
      context.lineCap = 'round';
      context.lineJoin = 'round';
      redrawCanvas(tempElements);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    redrawCanvas(elements);
  }, [elements]);

  const redrawCanvas = useCallback((elementsToRender: DrawingElement[]) => {
    if (!contextRef.current || !canvasRef.current) return;

    const context = contextRef.current;
    const canvas = canvasRef.current;

    // Clear canvas
    context.clearRect(0, 0, canvas.width, canvas.height);

    // Draw all elements
    elementsToRender.forEach((element) => {
      drawElement(context, element);
    });
  }, []);

  const drawElement = (context: CanvasRenderingContext2D, element: DrawingElement) => {
    context.strokeStyle = element.color;
    context.lineWidth = element.strokeWidth;
    context.fillStyle = element.fillColor || 'transparent';

    switch (element.type) {
      case 'pen':
        if (element.path && element.path.length > 1) {
          context.beginPath();
          context.moveTo(element.path[0].x, element.path[0].y);
          for (let i = 1; i < element.path.length; i++) {
            context.lineTo(element.path[i].x, element.path[i].y);
          }
          context.stroke();
        }
        break;
      case 'rectangle':
        if (element.width && element.height) {
          context.beginPath();
          context.rect(element.startX, element.startY, element.width, element.height);
          if (element.fillColor) context.fill();
          context.stroke();
        }
        break;
      case 'circle':
        if (element.width && element.height) {
          const radius = Math.sqrt(element.width * element.width + element.height * element.height) / 2;
          context.beginPath();
          context.arc(element.startX + element.width / 2, element.startY + element.height / 2, Math.abs(radius), 0, 2 * Math.PI);
          if (element.fillColor) context.fill();
          context.stroke();
        }
        break;
      case 'triangle':
        if (element.width && element.height) {
          context.beginPath();
          context.moveTo(element.startX + element.width / 2, element.startY);
          context.lineTo(element.startX, element.startY + element.height);
          context.lineTo(element.startX + element.width, element.startY + element.height);
          context.closePath();
          if (element.fillColor) context.fill();
          context.stroke();
        }
        break;
      case 'line':
        if (element.endX !== undefined && element.endY !== undefined) {
          context.beginPath();
          context.moveTo(element.startX, element.startY);
          context.lineTo(element.endX, element.endY);
          context.stroke();
        }
        break;
      case 'arrow':
        if (element.endX !== undefined && element.endY !== undefined) {
          drawArrow(context, element.startX, element.startY, element.endX, element.endY);
        }
        break;
      case 'text':
        if (element.text) {
          context.font = '16px Inter, sans-serif';
          context.fillStyle = element.color;
          context.fillText(element.text, element.startX, element.startY);
        }
        break;
    }
  };

  const drawArrow = (context: CanvasRenderingContext2D, startX: number, startY: number, endX: number, endY: number) => {
    const headLength = 15;
    const angle = Math.atan2(endY - startY, endX - startX);

    // Draw line
    context.beginPath();
    context.moveTo(startX, startY);
    context.lineTo(endX, endY);
    context.stroke();

    // Draw arrowhead
    context.beginPath();
    context.moveTo(endX, endY);
    context.lineTo(endX - headLength * Math.cos(angle - Math.PI / 6), endY - headLength * Math.sin(angle - Math.PI / 6));
    context.moveTo(endX, endY);
    context.lineTo(endX - headLength * Math.cos(angle + Math.PI / 6), endY - headLength * Math.sin(angle + Math.PI / 6));
    context.stroke();
  };

  const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const pos = getMousePos(e);
    setIsDrawing(true);
    setStartPoint(pos);

    if (tool.type === 'pen') {
      setCurrentPath([pos]);
    } else if (tool.type === 'text') {
      const text = prompt('Enter text:');
      if (text) {
        const newElement: DrawingElement = {
          id: uuidv4(),
          type: 'text',
          startX: pos.x,
          startY: pos.y,
          color: tool.color,
          strokeWidth: tool.strokeWidth,
          text,
        };
        setElements(prev => [...prev, newElement]);
      }
    } else if (tool.type === 'eraser') {
      // Find element to erase
      const elementToRemove = findElementAtPosition(pos.x, pos.y);
      if (elementToRemove) {
        setElements(prev => prev.filter(el => el.id !== elementToRemove.id));
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint) return;

    const pos = getMousePos(e);

    if (tool.type === 'pen') {
      setCurrentPath(prev => [...prev, pos]);

      // Draw current path in real-time
      if (contextRef.current && currentPath.length > 0) {
        const context = contextRef.current;
        context.strokeStyle = tool.color;
        context.lineWidth = tool.strokeWidth;
        context.beginPath();
        context.moveTo(currentPath[currentPath.length - 1].x, currentPath[currentPath.length - 1].y);
        context.lineTo(pos.x, pos.y);
        context.stroke();
      }
    } else {
      // For shapes, we'll draw a preview
      redrawCanvas(elements);
      if (contextRef.current) {
        drawPreviewShape(contextRef.current, startPoint, pos);
      }
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !startPoint) return;

    const pos = getMousePos(e);
    setIsDrawing(false);

    if (tool.type === 'pen' && currentPath.length > 1) {
      const newElement: DrawingElement = {
        id: uuidv4(),
        type: 'pen',
        startX: startPoint.x,
        startY: startPoint.y,
        color: tool.color,
        strokeWidth: tool.strokeWidth,
        path: [...currentPath, pos],
      };
      setElements(prev => [...prev, newElement]);
      setCurrentPath([]);
    } else if (['rectangle', 'circle', 'triangle', 'line', 'arrow'].includes(tool.type)) {
      const width = pos.x - startPoint.x;
      const height = pos.y - startPoint.y;

      if (Math.abs(width) > 5 || Math.abs(height) > 5) { // Minimum size threshold
        const newElement: DrawingElement = {
          id: uuidv4(),
          type: tool.type,
          startX: startPoint.x,
          startY: startPoint.y,
          endX: pos.x,
          endY: pos.y,
          width: width,
          height: height,
          color: tool.color,
          strokeWidth: tool.strokeWidth,
          fillColor: tool.fillColor,
        };
        setElements(prev => [...prev, newElement]);
      }
    }

    setStartPoint(null);
  };

  const drawPreviewShape = (context: CanvasRenderingContext2D, start: { x: number; y: number }, end: { x: number; y: number }) => {
    context.strokeStyle = tool.color;
    context.lineWidth = tool.strokeWidth;
    context.fillStyle = tool.fillColor || 'transparent';
    context.setLineDash([5, 5]); // Dashed line for preview

    const width = end.x - start.x;
    const height = end.y - start.y;

    switch (tool.type) {
      case 'rectangle':
        context.beginPath();
        context.rect(start.x, start.y, width, height);
        if (tool.fillColor) context.fill();
        context.stroke();
        break;
      case 'circle':
        const radius = Math.sqrt(width * width + height * height) / 2;
        context.beginPath();
        context.arc(start.x + width / 2, start.y + height / 2, Math.abs(radius), 0, 2 * Math.PI);
        if (tool.fillColor) context.fill();
        context.stroke();
        break;
      case 'triangle':
        context.beginPath();
        context.moveTo(start.x + width / 2, start.y);
        context.lineTo(start.x, start.y + height);
        context.lineTo(start.x + width, start.y + height);
        context.closePath();
        if (tool.fillColor) context.fill();
        context.stroke();
        break;
      case 'line':
        context.beginPath();
        context.moveTo(start.x, start.y);
        context.lineTo(end.x, end.y);
        context.stroke();
        break;
      case 'arrow':
        drawArrow(context, start.x, start.y, end.x, end.y);
        break;
    }

    context.setLineDash([]); // Reset line dash
  };

  const findElementAtPosition = (x: number, y: number): DrawingElement | null => {
    // Simple hit detection - can be improved
    for (let i = elements.length - 1; i >= 0; i--) {
      const element = elements[i];
      if (element.type === 'pen' && element.path) {
        // Check if point is near any point in the path
        for (const point of element.path) {
          if (Math.abs(point.x - x) < 10 && Math.abs(point.y - y) < 10) {
            return element;
          }
        }
      } else if (element.width && element.height) {
        // Check if point is within bounding box
        if (x >= element.startX && x <= element.startX + element.width &&
            y >= element.startY && y <= element.startY + element.height) {
          return element;
        }
      }
    }
    return null;
  };

  // Export functions
  const exportAsImage = () => {
    if (!canvasRef.current) return;

    const dataURL = canvasRef.current.toDataURL('image/png');
    const link = document.createElement('a');
    link.download = `whiteboard-${Date.now()}.png`;
    link.href = dataURL;
    link.click();
  };

  const exportAsSVG = () => {
    if (!canvasRef.current) return;

    // Create SVG representation
    const canvas = canvasRef.current;
    const svg = `
      <svg width="${canvas.width}" height="${canvas.height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        ${elements.map(element => elementToSVG(element)).join('')}
      </svg>
    `;

    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.download = `whiteboard-${Date.now()}.svg`;
    link.href = url;
    link.click();

    URL.revokeObjectURL(url);
  };

  const elementToSVG = (element: DrawingElement): string => {
    switch (element.type) {
      case 'pen':
        if (!element.path || element.path.length < 2) return '';
        const pathData = `M ${element.path[0].x} ${element.path[0].y} ` +
          element.path.slice(1).map(p => `L ${p.x} ${p.y}`).join(' ');
        return `<path d="${pathData}" stroke="${element.color}" stroke-width="${element.strokeWidth}" fill="none"/>`;
      case 'rectangle':
        return `<rect x="${element.startX}" y="${element.startY}" width="${element.width}" height="${element.height}"
                stroke="${element.color}" stroke-width="${element.strokeWidth}" fill="${element.fillColor || 'none'}"/>`;
      case 'circle':
        const radius = Math.sqrt((element.width || 0) * (element.width || 0) + (element.height || 0) * (element.height || 0)) / 2;
        return `<circle cx="${element.startX + (element.width || 0) / 2}" cy="${element.startY + (element.height || 0) / 2}"
                r="${Math.abs(radius)}" stroke="${element.color}" stroke-width="${element.strokeWidth}" fill="${element.fillColor || 'none'}"/>`;
      case 'line':
        return `<line x1="${element.startX}" y1="${element.startY}" x2="${element.endX}" y2="${element.endY}"
                stroke="${element.color}" stroke-width="${element.strokeWidth}"/>`;
      case 'text':
        return `<text x="${element.startX}" y="${element.startY}" fill="${element.color}" font-size="16" font-family="Inter, sans-serif">
                ${element.text || ''}</text>`;
      default:
        return '';
    }
  };

  const clearCanvas = () => {
    setElements([]);
  };

  const undo = () => {
    setElements(prev => prev.slice(0, -1));
  };

  // Expose functions to parent component
  React.useImperativeHandle(ref, () => ({
    exportAsImage,
    exportAsSVG,
    clearCanvas,
    undo,
  }));

  const getCursor = () => {
    switch (tool.type) {
      case 'pen':
      case 'eraser':
        return 'crosshair';
      case 'text':
        return 'text';
      default:
        return 'crosshair';
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      <canvas
        ref={canvasRef}
        className="border-none outline-none"
        style={{ cursor: getCursor() }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
      />
    </div>
  );
});

WhiteboardCanvas.displayName = 'WhiteboardCanvas';

export default WhiteboardCanvas;
