'use client';

import React, { useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { v4 as uuidv4 } from 'uuid';

export interface WhiteboardTool {
  type: 'pen' | 'rectangle' | 'circle' | 'triangle' | 'arrow' | 'line' | 'text' | 'eraser' | 'select';
  color: string;
  strokeWidth: number;
  fillColor?: string;
}

interface WhiteboardCanvasProps {
  tool: WhiteboardTool;
  onToolChange?: (tool: WhiteboardTool) => void;
}

interface WhiteboardCanvasRef {
  exportAsImage: () => void;
  exportAsSVG: () => void;
  clearCanvas: () => void;
  undo: () => void;
}

const WhiteboardCanvas = React.forwardRef<WhiteboardCanvasRef, WhiteboardCanvasProps>(
  ({ tool, onToolChange }, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Initialize Fabric.js canvas
    const canvas = new fabric.Canvas(canvasRef.current, {
      width: window.innerWidth,
      height: window.innerHeight - 160, // Account for toolbar height
      backgroundColor: '#ffffff',
      selection: tool.type === 'select',
    });

    fabricCanvasRef.current = canvas;

    // Set up drawing mode based on tool
    setupDrawingMode(canvas, tool);

    // Handle window resize
    const handleResize = () => {
      canvas.setDimensions({
        width: window.innerWidth,
        height: window.innerHeight - 160,
      });
      canvas.renderAll();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      canvas.dispose();
    };
  }, []);

  useEffect(() => {
    if (fabricCanvasRef.current) {
      setupDrawingMode(fabricCanvasRef.current, tool);
    }
  }, [tool]);

  const setupDrawingMode = (canvas: fabric.Canvas, currentTool: WhiteboardTool) => {
    // Clear all event listeners
    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');
    canvas.off('path:created');

    // Reset canvas state
    canvas.isDrawingMode = false;
    canvas.selection = currentTool.type === 'select';
    canvas.defaultCursor = 'default';

    switch (currentTool.type) {
      case 'pen':
        setupPenTool(canvas, currentTool);
        break;
      case 'rectangle':
      case 'circle':
      case 'triangle':
      case 'line':
      case 'arrow':
        setupShapeTool(canvas, currentTool);
        break;
      case 'text':
        setupTextTool(canvas, currentTool);
        break;
      case 'eraser':
        setupEraserTool(canvas, currentTool);
        break;
      case 'select':
        setupSelectTool(canvas);
        break;
    }
  };

  const setupPenTool = (canvas: fabric.Canvas, currentTool: WhiteboardTool) => {
    canvas.isDrawingMode = true;
    canvas.freeDrawingBrush.color = currentTool.color;
    canvas.freeDrawingBrush.width = currentTool.strokeWidth;
    canvas.defaultCursor = 'crosshair';
  };

  const setupShapeTool = (canvas: fabric.Canvas, currentTool: WhiteboardTool) => {
    canvas.defaultCursor = 'crosshair';
    let shape: fabric.Object | null = null;

    canvas.on('mouse:down', (e) => {
      if (!e.pointer) return;
      setIsDrawing(true);
      setStartPoint({ x: e.pointer.x, y: e.pointer.y });
      
      // Create initial shape
      shape = createShape(currentTool.type, e.pointer.x, e.pointer.y, 0, 0, currentTool);
      if (shape) {
        canvas.add(shape);
      }
    });

    canvas.on('mouse:move', (e) => {
      if (!isDrawing || !startPoint || !e.pointer || !shape) return;

      const width = e.pointer.x - startPoint.x;
      const height = e.pointer.y - startPoint.y;

      updateShape(shape, currentTool.type, startPoint.x, startPoint.y, width, height);
      canvas.renderAll();
    });

    canvas.on('mouse:up', () => {
      setIsDrawing(false);
      setStartPoint(null);
      shape = null;
    });
  };

  const setupTextTool = (canvas: fabric.Canvas, currentTool: WhiteboardTool) => {
    canvas.defaultCursor = 'text';
    
    canvas.on('mouse:down', (e) => {
      if (!e.pointer) return;
      
      const text = new fabric.IText('Type here...', {
        left: e.pointer.x,
        top: e.pointer.y,
        fontFamily: 'Inter, sans-serif',
        fontSize: 16,
        fill: currentTool.color,
        editable: true,
      });
      
      canvas.add(text);
      canvas.setActiveObject(text);
      text.enterEditing();
    });
  };

  const setupEraserTool = (canvas: fabric.Canvas, currentTool: WhiteboardTool) => {
    canvas.defaultCursor = 'crosshair';
    
    canvas.on('mouse:down', (e) => {
      const target = canvas.findTarget(e.e, false);
      if (target && target !== canvas) {
        canvas.remove(target);
      }
    });
  };

  const setupSelectTool = (canvas: fabric.Canvas) => {
    canvas.selection = true;
    canvas.defaultCursor = 'default';
  };

  const createShape = (
    type: string,
    x: number,
    y: number,
    width: number,
    height: number,
    currentTool: WhiteboardTool
  ): fabric.Object | null => {
    const commonProps = {
      left: x,
      top: y,
      stroke: currentTool.color,
      strokeWidth: currentTool.strokeWidth,
      fill: currentTool.fillColor || 'transparent',
      selectable: true,
    };

    switch (type) {
      case 'rectangle':
        return new fabric.Rect({
          ...commonProps,
          width: Math.abs(width),
          height: Math.abs(height),
        });
      case 'circle':
        const radius = Math.sqrt(width * width + height * height) / 2;
        return new fabric.Circle({
          ...commonProps,
          radius: Math.abs(radius),
        });
      case 'triangle':
        return new fabric.Triangle({
          ...commonProps,
          width: Math.abs(width),
          height: Math.abs(height),
        });
      case 'line':
        return new fabric.Line([x, y, x + width, y + height], {
          ...commonProps,
          fill: undefined,
        });
      case 'arrow':
        // Create arrow using a group of line and triangle
        const line = new fabric.Line([0, 0, width, height], {
          stroke: currentTool.color,
          strokeWidth: currentTool.strokeWidth,
        });
        
        const arrowHead = new fabric.Triangle({
          width: 10,
          height: 10,
          fill: currentTool.color,
          left: width - 5,
          top: height - 5,
          angle: Math.atan2(height, width) * 180 / Math.PI + 90,
        });
        
        return new fabric.Group([line, arrowHead], {
          left: x,
          top: y,
          selectable: true,
        });
      default:
        return null;
    }
  };

  const updateShape = (
    shape: fabric.Object,
    type: string,
    x: number,
    y: number,
    width: number,
    height: number
  ) => {
    switch (type) {
      case 'rectangle':
        (shape as fabric.Rect).set({
          left: width < 0 ? x + width : x,
          top: height < 0 ? y + height : y,
          width: Math.abs(width),
          height: Math.abs(height),
        });
        break;
      case 'circle':
        const radius = Math.sqrt(width * width + height * height) / 2;
        (shape as fabric.Circle).set({
          radius: Math.abs(radius),
        });
        break;
      case 'triangle':
        (shape as fabric.Triangle).set({
          left: width < 0 ? x + width : x,
          top: height < 0 ? y + height : y,
          width: Math.abs(width),
          height: Math.abs(height),
        });
        break;
      case 'line':
        (shape as fabric.Line).set({
          x2: x + width,
          y2: y + height,
        });
        break;
      case 'arrow':
        // Update arrow group
        const group = shape as fabric.Group;
        const objects = group.getObjects();
        if (objects.length >= 2) {
          const line = objects[0] as fabric.Line;
          const arrowHead = objects[1] as fabric.Triangle;
          
          line.set({
            x2: width,
            y2: height,
          });
          
          arrowHead.set({
            left: width - 5,
            top: height - 5,
            angle: Math.atan2(height, width) * 180 / Math.PI + 90,
          });
        }
        break;
    }
  };

  // Export functions
  const exportAsImage = () => {
    if (!fabricCanvasRef.current) return;
    
    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1,
    });
    
    const link = document.createElement('a');
    link.download = `whiteboard-${Date.now()}.png`;
    link.href = dataURL;
    link.click();
  };

  const exportAsSVG = () => {
    if (!fabricCanvasRef.current) return;
    
    const svg = fabricCanvasRef.current.toSVG();
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = `whiteboard-${Date.now()}.svg`;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const clearCanvas = () => {
    if (!fabricCanvasRef.current) return;
    fabricCanvasRef.current.clear();
    fabricCanvasRef.current.backgroundColor = '#ffffff';
  };

  const undo = () => {
    if (!fabricCanvasRef.current) return;
    const objects = fabricCanvasRef.current.getObjects();
    if (objects.length > 0) {
      fabricCanvasRef.current.remove(objects[objects.length - 1]);
    }
  };

  // Expose functions to parent component
  React.useImperativeHandle(ref, () => ({
    exportAsImage,
    exportAsSVG,
    clearCanvas,
    undo,
  }));

  return (
    <div className="relative w-full h-full overflow-hidden">
      <canvas
        ref={canvasRef}
        className="border-none outline-none"
        style={{ cursor: fabricCanvasRef.current?.defaultCursor }}
      />
    </div>
  );
});

WhiteboardCanvas.displayName = 'WhiteboardCanvas';

export default WhiteboardCanvas;
