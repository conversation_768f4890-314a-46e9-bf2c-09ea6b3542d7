'use client';

import React, { useState, useRef, useEffect } from 'react';
import WhiteboardCanvas, { WhiteboardTool } from './WhiteboardCanvas';
import WhiteboardToolbar from './WhiteboardToolbar';

interface WhiteboardProps {
  title?: string;
  onTitleChange?: (title: string) => void;
  onSave?: (data: any) => void;
}

export default function Whiteboard({ title = 'Untitled Whiteboard', onTitleChange, onSave }: WhiteboardProps) {
  const [currentTool, setCurrentTool] = useState<WhiteboardTool>({
    type: 'pen',
    color: '#000000',
    strokeWidth: 3,
    fillColor: undefined,
  });
  
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [boardTitle, setBoardTitle] = useState(title);
  const canvasRef = useRef<{
    exportAsImage: () => void;
    exportAsSVG: () => void;
    clearCanvas: () => void;
    undo: () => void;
  }>(null);

  const handleToolChange = (tool: WhiteboardTool) => {
    setCurrentTool(tool);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            handleUndo();
            break;
          case 'a':
            e.preventDefault();
            handleClear();
            break;
        }
      } else {
        switch (e.key.toLowerCase()) {
          case 'v':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'select' });
            break;
          case 'p':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'pen' });
            break;
          case 'r':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'rectangle' });
            break;
          case 'c':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'circle' });
            break;
          case 't':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'text' });
            break;
          case 'l':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'line' });
            break;
          case 'e':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'eraser' });
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTool]);

  const handleTitleSubmit = () => {
    setIsEditingTitle(false);
    onTitleChange?.(boardTitle);
  };

  const handleTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSubmit();
    } else if (e.key === 'Escape') {
      setBoardTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleExportImage = () => {
    canvasRef.current?.exportAsImage();
  };

  const handleExportSVG = () => {
    canvasRef.current?.exportAsSVG();
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear the entire canvas? This action cannot be undone.')) {
      canvasRef.current?.clearCanvas();
    }
  };

  const handleUndo = () => {
    canvasRef.current?.undo();
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    alert('Sharing functionality will be implemented in the next phase!');
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    onSave?.({
      title: boardTitle,
      tool: currentTool,
      timestamp: new Date().toISOString(),
    });
    alert('Whiteboard saved successfully!');
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {isEditingTitle ? (
              <input
                type="text"
                value={boardTitle}
                onChange={(e) => setBoardTitle(e.target.value)}
                onBlur={handleTitleSubmit}
                onKeyDown={handleTitleKeyPress}
                className="text-xl font-semibold bg-transparent border-b-2 border-blue-500 outline-none text-gray-900 dark:text-white"
                autoFocus
              />
            ) : (
              <h1
                onClick={() => setIsEditingTitle(true)}
                className="text-xl font-semibold text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 transition-colors"
                title="Click to edit title"
              >
                {boardTitle}
              </h1>
            )}
            <div className="flex items-center space-x-2">
              <span className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Auto-saved
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={handleSave}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              Save
            </button>
            <button
              onClick={handleShare}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Share
            </button>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <WhiteboardToolbar
        tool={currentTool}
        onToolChange={handleToolChange}
        onExportImage={handleExportImage}
        onExportSVG={handleExportSVG}
        onClear={handleClear}
        onUndo={handleUndo}
      />

      {/* Canvas */}
      <div className="flex-1 relative overflow-hidden">
        <WhiteboardCanvas
          ref={canvasRef}
          tool={currentTool}
          onToolChange={setCurrentTool}
        />
        
        {/* Zoom Controls */}
        <div className="absolute bottom-6 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2">
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </button>
            <span className="text-sm text-gray-600 dark:text-gray-300 min-w-[50px] text-center">
              100%
            </span>
            <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 13H5v-2h14v2z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Tool Info */}
        <div className="absolute bottom-6 left-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-300">Tool:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                {currentTool.type}
              </span>
            </div>
            <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-300">Color:</span>
              <div
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: currentTool.color }}
              />
            </div>
            <div className="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-300">Size:</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {currentTool.strokeWidth}px
              </span>
            </div>
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="absolute top-6 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 max-w-xs">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Keyboard Shortcuts
          </h3>
          <div className="space-y-1 text-xs text-gray-600 dark:text-gray-300">
            <div className="flex justify-between">
              <span>Select Tool</span>
              <span className="font-mono">V</span>
            </div>
            <div className="flex justify-between">
              <span>Pen Tool</span>
              <span className="font-mono">P</span>
            </div>
            <div className="flex justify-between">
              <span>Rectangle</span>
              <span className="font-mono">R</span>
            </div>
            <div className="flex justify-between">
              <span>Circle</span>
              <span className="font-mono">C</span>
            </div>
            <div className="flex justify-between">
              <span>Text</span>
              <span className="font-mono">T</span>
            </div>
            <div className="flex justify-between">
              <span>Undo</span>
              <span className="font-mono">Ctrl+Z</span>
            </div>
            <div className="flex justify-between">
              <span>Clear All</span>
              <span className="font-mono">Ctrl+A</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
