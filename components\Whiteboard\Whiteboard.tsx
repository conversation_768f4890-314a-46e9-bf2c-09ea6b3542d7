'use client';

import React, { useState, useRef, useEffect } from 'react';
import WhiteboardCanvas, { WhiteboardTool } from './WhiteboardCanvas';
import ColorPicker from './ColorPicker';

interface WhiteboardProps {
  title?: string;
  onTitleChange?: (title: string) => void;
  onSave?: (data: any) => void;
}

export default function Whiteboard({ title = 'Untitled Whiteboard', onTitleChange, onSave }: WhiteboardProps) {
  const [currentTool, setCurrentTool] = useState<WhiteboardTool>({
    type: 'pen',
    color: '#000000',
    strokeWidth: 3,
    fillColor: undefined,
  });
  
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [boardTitle, setBoardTitle] = useState(title);
  const canvasRef = useRef<{
    exportAsImage: () => void;
    exportAsSVG: () => void;
    clearCanvas: () => void;
    undo: () => void;
  }>(null);

  const handleToolChange = (tool: WhiteboardTool) => {
    setCurrentTool(tool);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            handleUndo();
            break;
          case 'a':
            e.preventDefault();
            handleClear();
            break;
        }
      } else {
        switch (e.key.toLowerCase()) {
          case 'v':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'select' });
            break;
          case 'p':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'pen' });
            break;
          case 'r':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'rectangle' });
            break;
          case 'c':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'circle' });
            break;
          case 't':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'text' });
            break;
          case 'l':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'line' });
            break;
          case 'e':
            e.preventDefault();
            setCurrentTool({ ...currentTool, type: 'eraser' });
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTool]);

  const handleTitleSubmit = () => {
    setIsEditingTitle(false);
    onTitleChange?.(boardTitle);
  };

  const handleTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTitleSubmit();
    } else if (e.key === 'Escape') {
      setBoardTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleExportImage = () => {
    canvasRef.current?.exportAsImage();
  };

  const handleExportSVG = () => {
    canvasRef.current?.exportAsSVG();
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear the entire canvas? This action cannot be undone.')) {
      canvasRef.current?.clearCanvas();
    }
  };

  const handleUndo = () => {
    canvasRef.current?.undo();
  };

  const handleShare = () => {
    // TODO: Implement sharing functionality
    alert('Sharing functionality will be implemented in the next phase!');
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    onSave?.({
      title: boardTitle,
      tool: currentTool,
      timestamp: new Date().toISOString(),
    });
    alert('Whiteboard saved successfully!');
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            {isEditingTitle ? (
              <input
                type="text"
                value={boardTitle}
                onChange={(e) => setBoardTitle(e.target.value)}
                onBlur={handleTitleSubmit}
                onKeyDown={handleTitleKeyPress}
                className="text-lg font-semibold bg-transparent border-b-2 border-blue-500 outline-none text-gray-900 w-full"
                autoFocus
              />
            ) : (
              <h1
                onClick={() => setIsEditingTitle(true)}
                className="text-lg font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                title="Click to edit title"
              >
                {boardTitle}
              </h1>
            )}
          </div>

          <div className="flex items-center space-x-2 mb-4">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            <span className="text-xs text-gray-600">Auto-saved</span>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="flex-1 bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
            >
              Save
            </button>
            <button
              onClick={handleShare}
              className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              Share
            </button>
          </div>
        </div>

        {/* Tools Section */}
        <div className="flex-1 px-6 py-4 overflow-y-auto">
          {/* Tool Selection */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Tools</h3>
            <div className="grid grid-cols-3 gap-2">
              {[
                { type: 'select', icon: '↖️', label: 'Select' },
                { type: 'pen', icon: '✏️', label: 'Pen' },
                { type: 'rectangle', icon: '⬜', label: 'Rectangle' },
                { type: 'circle', icon: '⭕', label: 'Circle' },
                { type: 'triangle', icon: '🔺', label: 'Triangle' },
                { type: 'line', icon: '📏', label: 'Line' },
                { type: 'arrow', icon: '➡️', label: 'Arrow' },
                { type: 'text', icon: '📝', label: 'Text' },
                { type: 'eraser', icon: '🧽', label: 'Eraser' },
              ].map((tool) => (
                <button
                  key={tool.type}
                  onClick={() => handleToolChange({ ...currentTool, type: tool.type as any })}
                  className={`p-3 rounded-lg border transition-all ${
                    currentTool.type === tool.type
                      ? 'bg-blue-50 border-blue-300 text-blue-700'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100 text-gray-700'
                  }`}
                  title={tool.label}
                >
                  <div className="text-lg">{tool.icon}</div>
                  <div className="text-xs mt-1">{tool.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Color Section */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Colors</h3>
            <div className="space-y-3">
              <ColorPicker
                color={currentTool.color}
                onChange={(color) => handleToolChange({ ...currentTool, color })}
                label="Stroke Color"
              />

              {['rectangle', 'circle', 'triangle'].includes(currentTool.type) && (
                <ColorPicker
                  color={currentTool.fillColor || ''}
                  onChange={(color) => handleToolChange({ ...currentTool, fillColor: color || undefined })}
                  label="Fill Color"
                  allowTransparent={true}
                />
              )}
            </div>
          </div>

          {/* Stroke Width */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Stroke Width</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Size</span>
                <span className="text-xs text-gray-900 font-medium">{currentTool.strokeWidth}px</span>
              </div>
              <input
                type="range"
                min="1"
                max="20"
                value={currentTool.strokeWidth}
                onChange={(e) => handleToolChange({ ...currentTool, strokeWidth: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1px</span>
                <span>20px</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Actions</h3>
            <div className="space-y-2">
              <button
                onClick={handleUndo}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg transition-colors text-sm flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                </svg>
                <span>Undo</span>
              </button>

              <button
                onClick={handleClear}
                className="w-full bg-red-50 hover:bg-red-100 text-red-700 px-3 py-2 rounded-lg transition-colors text-sm flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                </svg>
                <span>Clear Canvas</span>
              </button>
            </div>
          </div>

          {/* Export */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Export</h3>
            <div className="space-y-2">
              <button
                onClick={handleExportImage}
                className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-3 py-2 rounded-lg transition-colors text-sm"
              >
                Export as PNG
              </button>
              <button
                onClick={handleExportSVG}
                className="w-full bg-purple-50 hover:bg-purple-100 text-purple-700 px-3 py-2 rounded-lg transition-colors text-sm"
              >
                Export as SVG
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden bg-white">
          <WhiteboardCanvas
            ref={canvasRef}
            tool={currentTool}
            onToolChange={setCurrentTool}
          />

          {/* Zoom Controls */}
          <div className="absolute bottom-6 right-6 bg-white rounded-lg shadow-lg border border-gray-200 p-2">
            <div className="flex items-center space-x-2">
              <button className="p-2 hover:bg-gray-100 rounded text-gray-700">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
              </button>
              <span className="text-sm text-gray-600 min-w-[50px] text-center">
                100%
              </span>
              <button className="p-2 hover:bg-gray-100 rounded text-gray-700">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 13H5v-2h14v2z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
