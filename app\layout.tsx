import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geist<PERSON><PERSON> = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Boardly - Ultimate Collaborative Whiteboard",
  description: "The ultimate collaborative whiteboard experience. Draw, design, and brainstorm with infinite possibilities. Like Excalidraw and Miro, but better.",
  keywords: ["whiteboard", "collaboration", "drawing", "design", "brainstorming", "visual", "team", "creative"],
  authors: [{ name: "Boardly Team" }],
  creator: "<PERSON><PERSON>",
  publisher: "Boardly",
  openGraph: {
    title: "Boardly - Ultimate Collaborative Whiteboard",
    description: "The ultimate collaborative whiteboard experience. Draw, design, and brainstorm with infinite possibilities.",
    url: "https://boardly.app",
    siteName: "Boardly",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Boardly - Ultimate Collaborative Whiteboard",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Boardly - Ultimate Collaborative Whiteboard",
    description: "The ultimate collaborative whiteboard experience. Draw, design, and brainstorm with infinite possibilities.",
    images: ["/og-image.png"],
    creator: "@boardlyapp",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
