'use client';

import React, { useState } from 'react';

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  label: string;
  allowTransparent?: boolean;
}

export default function ColorPicker({ color, onChange, label, allowTransparent = false }: ColorPickerProps) {
  const [showPicker, setShowPicker] = useState(false);

  const predefinedColors = [
    '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF',
    '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500', '#800080',
    '#FFC0CB', '#A52A2A', '#808080', '#000080', '#008000',
    '#800000', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE'
  ];

  return (
    <div>
      <label className="text-xs text-gray-600 mb-2 block">{label}</label>
      <div className="relative">
        <div className="flex items-center space-x-2">
          <div
            className="w-8 h-8 rounded-lg border-2 border-gray-300 cursor-pointer"
            style={{ 
              backgroundColor: color || 'transparent',
              backgroundImage: !color ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%)',
              backgroundSize: '8px 8px'
            }}
            onClick={() => setShowPicker(!showPicker)}
          />
          <span className="text-sm text-gray-700">{color || 'None'}</span>
        </div>
        
        {showPicker && (
          <div className="absolute top-10 left-0 z-50">
            <div
              className="fixed inset-0"
              onClick={() => setShowPicker(false)}
            />
            <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-3 w-64">
              {allowTransparent && (
                <button
                  onClick={() => {
                    onChange('');
                    setShowPicker(false);
                  }}
                  className="w-full mb-3 px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
                >
                  No Fill
                </button>
              )}
              
              <div className="grid grid-cols-5 gap-2 mb-3">
                {predefinedColors.map((presetColor) => (
                  <button
                    key={presetColor}
                    onClick={() => {
                      onChange(presetColor);
                      setShowPicker(false);
                    }}
                    className="w-8 h-8 rounded-lg border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: presetColor }}
                  />
                ))}
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-gray-600">Custom Color</label>
                <input
                  type="color"
                  value={color || '#000000'}
                  onChange={(e) => onChange(e.target.value)}
                  className="w-full h-8 rounded border border-gray-300"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
