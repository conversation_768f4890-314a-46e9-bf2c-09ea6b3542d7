'use client';

import React, { useState } from 'react';
import { WhiteboardTool } from './WhiteboardCanvas';
import { ChromePicker } from 'react-color';

interface WhiteboardToolbarProps {
  tool: WhiteboardTool;
  onToolChange: (tool: WhiteboardTool) => void;
  onExportImage?: () => void;
  onExportSVG?: () => void;
  onClear?: () => void;
  onUndo?: () => void;
}

export default function WhiteboardToolbar({
  tool,
  onToolChange,
  onExportImage,
  onExportSVG,
  onClear,
  onUndo,
}: WhiteboardToolbarProps) {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showFillColorPicker, setShowFillColorPicker] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);

  const tools = [
    { type: 'select', icon: '↖️', label: 'Select' },
    { type: 'pen', icon: '✏️', label: 'Pen' },
    { type: 'rectangle', icon: '⬜', label: 'Rectangle' },
    { type: 'circle', icon: '⭕', label: 'Circle' },
    { type: 'triangle', icon: '🔺', label: 'Triangle' },
    { type: 'line', icon: '📏', label: 'Line' },
    { type: 'arrow', icon: '➡️', label: 'Arrow' },
    { type: 'text', icon: '📝', label: 'Text' },
    { type: 'eraser', icon: '🧽', label: 'Eraser' },
  ];

  const predefinedColors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
    '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#FFC0CB',
    '#A52A2A', '#808080', '#000080', '#008000', '#800000',
  ];

  const strokeWidths = [1, 2, 3, 5, 8, 12, 16, 20];

  const handleToolSelect = (toolType: string) => {
    onToolChange({
      ...tool,
      type: toolType as WhiteboardTool['type'],
    });
  };

  const handleColorChange = (color: any) => {
    onToolChange({
      ...tool,
      color: color.hex,
    });
  };

  const handleFillColorChange = (color: any) => {
    onToolChange({
      ...tool,
      fillColor: color.hex,
    });
  };

  const handleStrokeWidthChange = (width: number) => {
    onToolChange({
      ...tool,
      strokeWidth: width,
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Tools */}
        <div className="flex items-center space-x-4">
          {/* Tool Selection */}
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            {tools.map((t) => (
              <button
                key={t.type}
                onClick={() => handleToolSelect(t.type)}
                className={`p-2 rounded-md transition-colors ${
                  tool.type === t.type
                    ? 'bg-blue-600 text-white'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
                }`}
                title={t.label}
              >
                <span className="text-sm">{t.icon}</span>
              </button>
            ))}
          </div>

          <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>

          {/* Stroke Color */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300">Stroke:</span>
            <div className="relative">
              <button
                onClick={() => setShowColorPicker(!showColorPicker)}
                className="w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600"
                style={{ backgroundColor: tool.color }}
              />
              {showColorPicker && (
                <div className="absolute top-10 left-0 z-50">
                  <div
                    className="fixed inset-0"
                    onClick={() => setShowColorPicker(false)}
                  />
                  <div className="bg-white rounded-lg shadow-lg p-3">
                    <div className="grid grid-cols-5 gap-2 mb-3">
                      {predefinedColors.map((color) => (
                        <button
                          key={color}
                          onClick={() => {
                            handleColorChange({ hex: color });
                            setShowColorPicker(false);
                          }}
                          className="w-6 h-6 rounded-full border border-gray-300"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <ChromePicker
                      color={tool.color}
                      onChange={handleColorChange}
                      disableAlpha
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Fill Color (for shapes) */}
          {['rectangle', 'circle', 'triangle'].includes(tool.type) && (
            <>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-300">Fill:</span>
                <div className="relative">
                  <button
                    onClick={() => setShowFillColorPicker(!showFillColorPicker)}
                    className="w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600"
                    style={{ 
                      backgroundColor: tool.fillColor || 'transparent',
                      backgroundImage: tool.fillColor ? 'none' : 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)',
                      backgroundSize: '8px 8px',
                      backgroundPosition: '0 0, 0 4px, 4px -4px, -4px 0px'
                    }}
                  />
                  {showFillColorPicker && (
                    <div className="absolute top-10 left-0 z-50">
                      <div
                        className="fixed inset-0"
                        onClick={() => setShowFillColorPicker(false)}
                      />
                      <div className="bg-white rounded-lg shadow-lg p-3">
                        <button
                          onClick={() => {
                            onToolChange({ ...tool, fillColor: undefined });
                            setShowFillColorPicker(false);
                          }}
                          className="w-full mb-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
                        >
                          No Fill
                        </button>
                        <div className="grid grid-cols-5 gap-2 mb-3">
                          {predefinedColors.map((color) => (
                            <button
                              key={color}
                              onClick={() => {
                                handleFillColorChange({ hex: color });
                                setShowFillColorPicker(false);
                              }}
                              className="w-6 h-6 rounded-full border border-gray-300"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                        <ChromePicker
                          color={tool.fillColor || '#ffffff'}
                          onChange={handleFillColorChange}
                          disableAlpha
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>

          {/* Stroke Width */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-300">Size:</span>
            <div className="flex items-center space-x-1">
              {strokeWidths.map((width) => (
                <button
                  key={width}
                  onClick={() => handleStrokeWidthChange(width)}
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    tool.strokeWidth === width
                      ? 'bg-blue-600 text-white'
                      : 'hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  <div
                    className="rounded-full bg-current"
                    style={{
                      width: Math.min(width, 16),
                      height: Math.min(width, 16),
                    }}
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onUndo}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            title="Undo"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
            </svg>
          </button>

          <button
            onClick={onClear}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-red-600 transition-colors"
            title="Clear Canvas"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
            </svg>
          </button>

          <div className="relative">
            <button
              onClick={() => setShowExportMenu(!showExportMenu)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Export
            </button>
            {showExportMenu && (
              <div className="absolute top-12 right-0 z-50">
                <div
                  className="fixed inset-0"
                  onClick={() => setShowExportMenu(false)}
                />
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 min-w-[120px]">
                  <button
                    onClick={() => {
                      onExportImage?.();
                      setShowExportMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    Export PNG
                  </button>
                  <button
                    onClick={() => {
                      onExportSVG?.();
                      setShowExportMenu(false);
                    }}
                    className="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    Export SVG
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
